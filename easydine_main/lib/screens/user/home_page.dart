import 'dart:ui';
import 'package:easydine_main/blocs/session/session_state.dart';
import 'package:easydine_main/blocs/settings/settings_bloc.dart';
import 'package:easydine_main/dialogs/rush_order_manager.dart';
import 'package:easydine_main/screens/user/rush_order_management_page.dart';
import 'package:easydine_main/widgets/settings_drawer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../blocs/session/session_bloc.dart';
import '../../blocs/session/session_event.dart';
import '../../models/buttonGridItem.dart';
import '../../router/router_constants.dart';
import '../../widgets/button_grid_home.dart';
import '../../widgets/current_settings_bar.dart';
import '../../widgets/section_header_home.dart';
import '../../widgets/tiled_background.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MenuButton {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final bool isHighlighted;

  MenuButton({
    required this.icon,
    required this.label,
    required this.onTap,
    this.isHighlighted = false,
  });
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  // Theme colors
  final Color _primaryColor = const Color(0xFF4CAF50);
  final Color _accentColor = const Color(0xFF009688);
  final Color _errorColor = const Color(0xFFE53935);
  final Color _surfaceColor = const Color(0xFFF5F5F5);

  // Key for the scaffold to access the drawer
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _showChecklistReminder = false;

  @override
  void initState() {
    super.initState();
    _checkForSkippedChecklist();
  }

  Future<void> _checkForSkippedChecklist() async {
    final prefs = await SharedPreferences.getInstance();
    final isSkipped = prefs.getBool('checklist_skipped') ?? false;
    final lastCheckDate = prefs.getString('last_check_date');
    final today = DateTime.now().toIso8601String().split('T')[0];

    // Check if all checklist items are completed
    bool allCompleted = true;
    for (String item in [
      'Opening Checks',
      'Chilled Storage Checks',
      'Cooking Temperature Checks',
      'Cooling Checks',
      'Reheating Temperature Checks'
    ]) {
      final isCompleted = prefs.getBool('checklist_$item') ?? false;
      print('$item completed: $isCompleted');
      if (!isCompleted) {
        allCompleted = false;
        break;
      }
    }

    setState(() {
      // Show reminder if checklist is not completed or was skipped
      _showChecklistReminder = isSkipped || !allCompleted;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final menuItems = [
      MenuButton(
        icon: Icons.point_of_sale,
        label: 'POS',
        onTap: () => _showPOSOptions(context),
      ),
      MenuButton(
        icon: Icons.receipt_long_outlined,
        label: 'Running Orders',
        onTap: () =>
            GoRouter.of(context).goNamed(RouterConstants.runningOrders),
      ),
      MenuButton(
        icon: Icons.analytics_outlined,
        label: 'Reports',
        onTap: () => GoRouter.of(context).goNamed(RouterConstants.reports),
      ),
      MenuButton(
        icon: Icons.help_outline_outlined,
        label: 'Support',
        onTap: () => GoRouter.of(context).goNamed(RouterConstants.support),
      ),
      MenuButton(
        icon: Icons.settings_outlined,
        label: 'Settings',
        onTap: () => GoRouter.of(context).goNamed(RouterConstants.settings),
      ),
    ];

    return Scaffold(
      key: _scaffoldKey,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        leading: ConstrainedBox(
          constraints:
              BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.2),
          child: Center(
            child: BlocBuilder<SessionBloc, SessionState>(
              builder: (context, state) {
                final waiterName = state.waiterName ?? '';
                final firstLetter =
                    waiterName.isNotEmpty ? waiterName[0].toUpperCase() : '?';

                return Stack(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [
                            Colors.orange.shade400,
                            Colors.deepOrange.shade600,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          firstLetter,
                          style: GoogleFonts.poppins(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.white,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
        leadingWidth: MediaQuery.of(context).size.width * 0.1,
        titleSpacing: 0,
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        title: Row(
          children: [
            // User info section
            Expanded(
              child: BlocBuilder<SessionBloc, SessionState>(
                builder: (context, state) {
                  final waiterName = state.waiterName ?? '';
                  return Row(
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                waiterName.isNotEmpty ? waiterName : 'Guest',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Icon(
                                Icons.verified,
                                color: Colors.blue,
                                size: 14,
                              ),
                            ],
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.purple.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              state.currentStaff?.role ?? '',
                              style: GoogleFonts.poppins(
                                color: Colors.white70,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Spacer(),
                      // App title
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.restaurant_menu, color: Colors.white),
                          SizedBox(width: 8),
                          Text(
                            'Restaurant POS',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                              fontSize: 24,
                            ),
                          ),
                        ],
                      ),
                      Spacer(),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
        actions: [
          BlocBuilder<SessionBloc, SessionState>(
            builder: (context, state) {
              return Container(
                margin: const EdgeInsets.only(right: 16, left: 8),
                child: IconButton(
                  icon: const Icon(Icons.power_settings_new),
                  tooltip: 'Logout',
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.red.withOpacity(0.2),
                    foregroundColor: Colors.red[400],
                    padding: const EdgeInsets.all(8),
                  ),
                  onPressed: () => _showLogoutConfirmation(context),
                ),
              );
            },
          ),
        ],
      ),
      // End drawer implementation
      endDrawer: BuildSettingsDrawer(),
      body: Stack(
        children: [
          TiledBackground(),
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 32, sigmaY: 32),
            child: BlocBuilder<SettingsBloc, SettingsState>(
              builder: (context, state) {
                // Add Rush Orders button if enabled
                final List<MenuButton> allButtons = [...menuItems];
                if (state.rushOrderEnabled) {
                  allButtons.insert(
                      2,
                      MenuButton(
                        icon: Icons.speed_outlined,
                        label: 'Rush Orders',
                        onTap: () => GoRouter.of(context)
                            .goNamed(RouterConstants.rushOrders),
                        isHighlighted: true,
                      ));
                }

                return SafeArea(
                  child: Column(
                    children: [
                      if (_showChecklistReminder)
                        GestureDetector(
                          onTap: () => GoRouter.of(context)
                              .goNamed(RouterConstants.dailyChecklist),
                          child: Container(
                            margin: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            padding: EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Colors.orange.withOpacity(0.9),
                                  Colors.deepOrange.withOpacity(0.9),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black26,
                                  blurRadius: 8,
                                  offset: Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.warning_amber_rounded,
                                  color: Colors.white,
                                ),
                                SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'Daily checklist is incomplete. Tap to complete now.',
                                    style: GoogleFonts.poppins(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ],
                            ),
                          ),
                        ),
                      Center(
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            maxWidth: isLandscape
                                ? MediaQuery.of(context).size.width * 0.6
                                : MediaQuery.of(context).size.width * 0.8,
                            maxHeight: MediaQuery.of(context).size.height * 0.7,
                          ),
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 20.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                GridView.count(
                                  shrinkWrap: true,
                                  crossAxisCount: 3,
                                  mainAxisSpacing: 20,
                                  crossAxisSpacing: 20,
                                  children: allButtons
                                      .map((button) => _buildMainButton(
                                            icon: button.icon,
                                            label: button.label,
                                            onTap: button.onTap,
                                            isHighlighted: button.isHighlighted,
                                            width: 150,
                                          ))
                                      .toList(),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required double width,
    bool isHighlighted = false,
  }) {
    return Container(
      width: width,
      height: width,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isHighlighted ? Colors.orange : Colors.white24,
                width: isHighlighted ? 2 : 1,
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withOpacity(0.1),
                  Colors.white.withOpacity(0.05),
                ],
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: 32,
                  color: isHighlighted ? Colors.orange : Colors.white,
                ),
                SizedBox(height: 12),
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    color: isHighlighted ? Colors.orange : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showPOSOptions(BuildContext context) {
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      transitionDuration: Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: Tween<double>(begin: 0.5, end: 1.0).animate(
            CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutBack,
            ),
          ),
          child: FadeTransition(
            opacity: animation,
            child: Dialog(
              backgroundColor: Colors.grey[900],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: isLandscape
                      ? MediaQuery.of(context).size.width * 0.6
                      : MediaQuery.of(context).size.width * 0.8,
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.point_of_sale,
                            color: Colors.white,
                            size: 28,
                          ),
                          SizedBox(width: 12),
                          Text(
                            'Select Order Type',
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Spacer(),
                          IconButton(
                            icon: Icon(Icons.close, color: Colors.white70),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ],
                      ),
                      SizedBox(height: 24),
                      GridView.count(
                        shrinkWrap: true,
                        crossAxisCount: 2,
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                        childAspectRatio: isLandscape ? 2 : 1.2,
                        children: [
                          _buildPOSOptionTile(
                            context: context,
                            icon: Icons.restaurant_outlined,
                            label: 'Dine In',
                            description: 'Table service orders',
                            onTap: () {
                              Navigator.pop(context);
                              GoRouter.of(context)
                                  .goNamed(RouterConstants.dineIn);
                            },
                          ),
                          _buildPOSOptionTile(
                            context: context,
                            icon: Icons.delivery_dining_outlined,
                            label: 'Delivery',
                            description: 'Home delivery orders',
                            onTap: () {
                              Navigator.pop(context);
                              GoRouter.of(context)
                                  .goNamed(RouterConstants.delivery);
                            },
                          ),
                          _buildPOSOptionTile(
                            context: context,
                            icon: Icons.takeout_dining_outlined,
                            label: 'Take Away',
                            description: 'Pickup orders',
                            onTap: () {
                              Navigator.pop(context);
                              GoRouter.of(context).goNamed(
                                RouterConstants.pos,
                                queryParameters: {
                                  'tableNumber': '1',
                                  'orderId': '1',
                                  'orderType': 'takeaway',
                                },
                              );
                            },
                          ),
                          _buildPOSOptionTile(
                            context: context,
                            icon: Icons.qr_code_scanner_outlined,
                            label: 'Contactless',
                            description: 'QR code ordering',
                            onTap: () {
                              Navigator.pop(context);
                              GoRouter.of(context)
                                  .goNamed(RouterConstants.contactlessDining);
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPOSOptionTile({
    required BuildContext context,
    required IconData icon,
    required String label,
    required String description,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white24),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              SizedBox(height: 12),
              Text(
                label,
                style: GoogleFonts.poppins(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4),
              Text(
                description,
                style: GoogleFonts.poppins(
                  color: Colors.white70,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showReportsMenu() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: _surfaceColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.analytics_outlined, color: _primaryColor),
              const SizedBox(width: 8),
              const Text("Reports"),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDialogListTile(
                icon: Icons.calendar_today_outlined,
                title: "Daily Sales",
                onTap: () {
                  // Navigate to daily sales report
                },
              ),
              _buildDialogListTile(
                icon: Icons.inventory_outlined,
                title: "Inventory Status",
                onTap: () {
                  // Navigate to inventory report
                },
              ),
              _buildDialogListTile(
                icon: Icons.fastfood_outlined,
                title: "Popular Items",
                onTap: () {
                  // Navigate to popular items report
                },
              ),
              _buildDialogListTile(
                icon: Icons.delivery_dining_outlined,
                title: "Delivery Analytics",
                onTap: () {
                  // Navigate to delivery analytics
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              child: const Text("Close"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _showSupportOptions() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: _surfaceColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.help_outline, color: _accentColor),
              const SizedBox(width: 8),
              const Text("Support Options"),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDialogListTile(
                icon: Icons.bug_report_outlined,
                title: "Report a Problem",
                onTap: () {
                  // Show problem reporting form
                },
              ),
              _buildDialogListTile(
                icon: Icons.error_outline,
                title: "System Status",
                onTap: () {
                  // Show system status page
                },
              ),
              _buildDialogListTile(
                icon: Icons.help_outline,
                title: "Help Center",
                onTap: () {
                  // Navigate to help center
                },
              ),
              _buildDialogListTile(
                icon: Icons.headset_mic_outlined,
                title: "Contact Support",
                onTap: () {
                  // Show contact options
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              child: const Text("Close"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildDialogListTile({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: _accentColor),
      title: Text(title),
      subtitle: subtitle != null ? Text(subtitle) : null,
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Container(
      margin: EdgeInsets.only(left: 4),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.white70,
            size: 24,
          ),
          SizedBox(width: 8),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 22,
              fontWeight: FontWeight.w600,
              color: Colors.white,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  void _showLogoutConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.grey[900],
          title: Text(
            'Confirm Logout',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: TextStyle(color: Colors.white70),
              ),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[400],
              ),
              onPressed: () {
                // Trigger session end
                context.read<SessionBloc>().add(EndSession());
                // Navigate to login
                GoRouter.of(context).goNamed(
                  RouterConstants.pinEntry,
                );
              },
              child: Text('Logout'),
            ),
          ],
        );
      },
    );
  }
}
