import '../models/menuItem.dart';

class MenuService {
  static final List<MenuItem> _allMenuItems = [
    // Pizza Category
 
  ];

  static List<MenuItem> getMenuItems(String category) {
    return _allMenuItems.where((item) => item.category == category).toList();
  }

  static List<MenuItem> getAllMenuItems() {
    return List.from(_allMenuItems);
  }

  static List<String> getAllCategories() {
    return _allMenuItems.map((item) => item.category).toSet().toList();
  }

  static MenuItem? getItemById(String id) {
    try {
      return _allMenuItems.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }
}
