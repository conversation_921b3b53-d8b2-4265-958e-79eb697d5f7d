import 'package:http_interceptor/http_interceptor.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

import '../config/env_config.dart';

typedef TokenExpiredCallback = void Function();
TokenExpiredCallback? _globalTokenExpiredCallback;

void setTokenExpiredCallback(TokenExpiredCallback callback) {
  _globalTokenExpiredCallback = callback;
}

class CustomInterceptor implements InterceptorContract {
  @override
  bool shouldInterceptRequest() => true;

  @override
  bool shouldInterceptResponse() => true;

  @override
  Future<BaseRequest> interceptRequest({required BaseRequest request}) async {
    // Add standard headers
    request.headers["x-host"] = EnvConfig.xHost;
    request.headers["Content-Type"] = "application/json";

    final prefs = await SharedPreferences.getInstance();

    // Determine which token to use based on request URL
    final requestUrl = request.url.toString();
    String? token;

    // Check if this is an attendance operation (clock/check in/out)
    if (_isAttendanceOperation(requestUrl)) {
      debugPrint(
          '🔑 HTTP Interceptor: Attendance operation - using manager token');
      token = prefs.getString('manager-token');
    } else if (_isStaffOperation(requestUrl)) {
      token = prefs.getString('staff-token');
      debugPrint('🔑 HTTP Interceptor: Using staff-token for staff operation');
    } else {
      token = prefs.getString('manager-token');
      debugPrint(
          '🔑 HTTP Interceptor: Using manager token for manager operation');
    }

    if (token != null) {
      request.headers["Authorization"] = "Bearer $token";
      request.headers["Cookie"] = "access-token=$token";
    }

    return request;
  }

  bool _isAttendanceOperation(String url) {
    // Attendance operations (clock in/out, check in/out) use manager tokens for authorization
    return url.contains('/attendance/');
  }

  bool _isStaffOperation(String url) {
    // Define staff-specific operations that might use staff tokens
    // NOTE: attendance operations are handled separately
    return url.contains('/staff/session') || url.contains('/staff/dashboard');
  }

  @override
  Future<BaseResponse> interceptResponse(
      {required BaseResponse response}) async {
    // Check for 401 Unauthorized (token expired)
    if (response.statusCode == 401) {
      debugPrint(
          '🚫 HTTP Interceptor: 401 Unauthorized - Token may be expired');
      debugPrint('🚫 HTTP Interceptor: Request URL: ${response.request?.url}');
      debugPrint('🚫 HTTP Interceptor: Response body: ${response.toString()}');

      final requestUrl = response.request?.url.toString() ?? '';
      final prefs = await SharedPreferences.getInstance();

      if (_isStaffOperation(requestUrl)) {
        debugPrint(
            '🔍 HTTP Interceptor: 401 from staff operation - clearing staff token only');

        // Clear only staff token for staff operations
        final staffTokenBefore = prefs.getString('staff-token');
        if (staffTokenBefore != null) {
          await prefs.remove('staff-token');
          debugPrint('🚫 HTTP Interceptor: Staff token cleared');
        }

        // Manager token remains untouched
        final managerToken = prefs.getString('manager-token');
        debugPrint(
            '✅ HTTP Interceptor: Manager token preserved: ${managerToken != null ? 'EXISTS' : 'NULL'}');
      } else {
        debugPrint(
            '🚫 HTTP Interceptor: 401 from manager operation - clearing manager token');

        // Clear manager token for manager operations
        final managerTokenBefore = prefs.getString('manager-token');
        if (managerTokenBefore != null) {
          await prefs.remove('manager-token');
          debugPrint('🚫 HTTP Interceptor: Manager token cleared');
        }

        // Also clear legacy access-token for backward compatibility
        await prefs.remove('access-token');
        await prefs.remove('token-timestamp');

        debugPrint(
            '🚫 HTTP Interceptor: Manager token cleared due to 401 response');

        // Trigger immediate token expiration callback
        if (_globalTokenExpiredCallback != null) {
          debugPrint(
              '🔄 HTTP Interceptor: Triggering token expiration callback');
          _globalTokenExpiredCallback!();
        }
      }
    }

    // Check for and save new tokens from response
    if (response.headers.containsKey('set-cookie')) {
      final cookieHeader = response.headers['set-cookie'];
      if (cookieHeader != null) {
        final token = _extractTokenFromCookies(cookieHeader);
        if (token != null) {
          final prefs = await SharedPreferences.getInstance();
          final requestUrl = response.request?.url.toString() ?? '';

          // Determine if this is a manager login response
          if (requestUrl.contains('/tenant/login')) {
            // Manager login - save as manager token
            await prefs.setString('manager-token', token);
            await prefs.setString(
                'manager-token-timestamp', DateTime.now().toIso8601String());
            debugPrint(
                '🔐 HTTP Interceptor: New manager token saved with timestamp');
          } else if (_isStaffOperation(requestUrl)) {
            // Staff operation - save as staff token
            await prefs.setString('staff-token', token);
            await prefs.setString(
                'staff-token-timestamp', DateTime.now().toIso8601String());
            debugPrint(
                '🔐 HTTP Interceptor: New staff token saved with timestamp');
          }

          // Also save in legacy location for backward compatibility
          await prefs.setString('access-token', token);
          await prefs.setString(
              'token-timestamp', DateTime.now().toIso8601String());
        }
      }
    }
    return response;
  }

  // Helper to extract token from cookies (handles multiple set-cookie headers)
  String? _extractTokenFromCookies(dynamic cookieHeader) {
    List<String> cookieParts = [];

    if (cookieHeader is List<String>) {
      cookieParts = cookieHeader;
      debugPrint(
          '🍪 HTTP Interceptor: Cookie header is List<String> with ${cookieParts.length} items');
    } else if (cookieHeader is String) {
      // Handle case where multiple set-cookie headers are concatenated
      if (cookieHeader.contains('set-cookie access-token=')) {
        // Handle case where multiple set-cookie headers are concatenated
        final setCookiePattern = RegExp(r'set-cookie\s+access-token=([^;]+)');
        final matches = setCookiePattern.allMatches(cookieHeader);
        cookieParts =
            matches.map((match) => 'access-token=${match.group(1)}').toList();
      } else {
        // Handle normal case - split by newlines first
        final lines = cookieHeader.split('\n');
        for (var line in lines) {
          if (line.trim().isNotEmpty) {
            cookieParts.addAll(line.split(';'));
          }
        }
      }
    }

    // Look for access-token in all cookie parts
    // Process in reverse order to get the latest token (in case of clearing + setting pattern)
    for (int i = cookieParts.length - 1; i >= 0; i--) {
      final part = cookieParts[i].trim();
      if (part.startsWith('access-token=')) {
        final token = part.substring('access-token='.length);
        // Skip empty tokens (clearing tokens)
        if (token.isNotEmpty && token != '' && token != 'null') {
          debugPrint(
              '🍪 HTTP Interceptor: Found valid token: ${token.substring(0, token.length > 20 ? 20 : token.length)}...');
          return token;
        } else {
          debugPrint('🍪 HTTP Interceptor: Skipping empty/clearing token');
        }
      }
    }

    debugPrint('🍪 HTTP Interceptor: No valid token found in cookies');
    return null;
  }
}
