# Menu Service Architecture

## Overview

This document describes the new menu service architecture that handles the complex menu data with availability information for the EasyDine application.

## Architecture Components

### 1. Custom Response Models (`/types/system/MenuResponse.types.ts`)

Defines TypeScript interfaces for the menu API response structure:

- `MenuApiResponse` - Complete API response wrapper
- `MenuWithAvailabilityResponse` - Main menu data structure
- `SectionResponse` - Menu section with dishes
- `DishResponse` - Individual dish with all details
- `AvailabilityInfo` - Availability status information
- `MenuAvailability` - Availability flags for different channels

### 2. Menu Service (`/services/menu.service.ts`)

Core service class that handles:

- **Data Loading**: Efficiently loads menu, branch, and business hour data
- **Availability Evaluation**: Uses optimized availability service for batch processing
- **Data Processing**: Handles upcoming changes, customizations, and filtering
- **Response Transformation**: Converts entity data to custom response format

Key methods:
- `getActiveMenuWithAvailability()` - Main method for fetching menu with availability
- `loadBranchWithBusinessHours()` - Loads branch data with business hours
- `loadActiveMenuWithRelations()` - Loads menu with all necessary relations
- `evaluateMenuAvailability()` - Evaluates availability for all menu items

### 3. Response Transformer (`/helpers/dish/menuResponseTransformer.helper.ts`)

Utility functions for transforming database entities to API response format:

- `transformMenuToResponse()` - Transforms FoodMenu entity
- `transformSectionToResponse()` - Transforms Section entity
- `transformDishToResponse()` - Transforms Dish entity
- `createMenuApiResponse()` - Creates complete API response structure

### 4. Updated Controller (`/controllers/foodmenu/foodMenu.controller.ts`)

Simplified controller that:
- Uses the MenuService for complex operations
- Handles error cases appropriately
- Returns properly formatted responses

## API Endpoint

**GET** `{{LAMBDA_HOST}}/{{food-menu-path}}/active-menu-withAvailability/?branchId=69a591fb-a51b-417f-9a38-d7d71239b418&pos=true`

### Query Parameters

- `branchId` (required) - UUID of the branch
- `pos` (optional) - Set to "true" for POS system requests (shows all items), "false" or omitted for customer app (filters unavailable items)

### Response Structure

```json
{
  "statusCode": 200,
  "success": true,
  "message": "Active food menu fetched successfully!",
  "data": {
    "foodMenuId": "string",
    "pictureUrl": "string",
    "isDefault": boolean,
    "name": "string",
    "description": "string",
    "createdAt": "ISO date string",
    "updatedAt": "ISO date string",
    "deletedAt": "ISO date string | null",
    "availability": {
      "site": boolean,
      "dineIn": boolean,
      "mobile": boolean,
      "pickup": boolean,
      "delivery": boolean,
      "takeAway": boolean,
      "phoneOrder": boolean,
      "contactlessDineIn": boolean
    },
    "visibility": boolean,
    "customTimes": boolean,
    "sections": [
      {
        "menuSectionId": "string",
        "pictureUrls": ["string"],
        "name": "string",
        "description": "string",
        "sectionIcon": {
          "sectionIconId": "string",
          "iconName": "string",
          "iconImg": "string"
        },
        "dishes": [
          {
            "dishId": "string",
            "pictureUrl": "string",
            "name": "string",
            "description": "string",
            "price": "string",
            "dietaryInfo": ["string"],
            "tags": ["string"],
            "isCustomizable": boolean,
            "preparationTime": number,
            "availability": { /* same as menu availability */ },
            "visibility": boolean,
            "specialRequests": boolean,
            "availabilityInfo": {
              "isAvailable": boolean,
              "reason": "string (optional)",
              "nextAvailableAt": "ISO date string (optional)",
              "availableUntil": "ISO date string (optional)"
            },
            "dishIngredients": [
              {
                "dishIngredientId": "string",
                "amount": "string",
                "ingredient": {
                  "ingredientId": "string",
                  "name": "string",
                  "description": "string",
                  "unit": "string"
                }
              }
            ],
            "allergies": [
              {
                "allergyId": "string",
                "name": "string",
                "description": "string",
                "colorPreference": "string"
              }
            ],
            "customization": "object (optional)"
          }
        ],
        "availabilityInfo": { /* same as dish availability info */ }
      }
    ],
    "customSlots": [],
    "globalCustomSlots": [],
    "specialDays": [],
    "availabilityInfo": { /* same as dish availability info */ }
  }
}
```

## Benefits

1. **Type Safety**: Strong TypeScript typing for all response structures
2. **Maintainability**: Separated concerns with dedicated service and transformer classes
3. **Performance**: Optimized data loading and batch processing
4. **Consistency**: Standardized response format across all menu endpoints
5. **Extensibility**: Easy to add new fields or modify response structure

## Usage Example

```typescript
// In controller
const menuService = new MenuService(queryRunner);
const menu = await menuService.getActiveMenuWithAvailability(branchId, forPOS);
ResponseHelper.success(res, OK, ACTIVE_FOODMENU_FETCHED, menu);
```

## Error Handling

The service throws descriptive errors that are caught and converted to appropriate HTTP responses:

- "Branch not found" → 404 NOT_FOUND
- "Active food menu not found" → 404 NOT_FOUND
- Other errors → 500 INTERNAL_SERVER_ERROR
