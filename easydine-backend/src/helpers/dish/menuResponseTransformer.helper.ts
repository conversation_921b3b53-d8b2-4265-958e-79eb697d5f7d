import { FoodMenu } from "../../models/foodmenu/foodmenu.model";
import { Section } from "../../models/foodmenu/section.model";
import { Dish } from "../../models/foodmenu/dish.model";
import { 
  MenuWithAvailabilityResponse, 
  SectionResponse, 
  DishResponse,
  MenuApiResponse,
  AvailabilityInfo,
  MenuAvailability,
  SectionIconResponse,
  DishIngredientResponse,
  AllergyResponse,
  IngredientResponse
} from "../../types/system/MenuResponse.types";

/**
 * Transform a FoodMenu entity with availability info into the custom response format
 */
export const transformMenuToResponse = (
  menu: FoodMenu & { availabilityInfo: AvailabilityInfo }
): MenuWithAvailabilityResponse => {
  return {
    foodMenuId: menu.foodMenuId,
    pictureUrl: menu.pictureUrl,
    isDefault: menu.isDefault,
    name: menu.name,
    description: menu.description,
    createdAt: menu.createdAt,
    updatedAt: menu.updatedAt,
    deletedAt: menu.deletedAt || null,
    availability: transformAvailability(menu.availability),
    visibility: menu.visibility,
    customTimes: menu.customTimes,
    sections: menu.sections ? menu.sections.map(transformSectionToResponse) : [],
    customSlots: menu.customSlots || [],
    globalCustomSlots: menu.globalCustomSlots || [],
    specialDays: menu.specialDays || [],
    availabilityInfo: menu.availabilityInfo
  };
};

/**
 * Transform a Section entity with availability info into the custom response format
 */
export const transformSectionToResponse = (
  section: Section & { availabilityInfo: AvailabilityInfo }
): SectionResponse => {
  return {
    menuSectionId: section.menuSectionId,
    pictureUrls: section.pictureUrls || [],
    name: section.name,
    description: section.description,
    createdAt: section.createdAt,
    updatedAt: section.updatedAt,
    deletedAt: section.deletedAt || null,
    availability: transformAvailability(section.availability),
    visibility: section.visibility,
    customTimes: section.customTimes,
    sectionIcon: section.sectionIcon ? transformSectionIconToResponse(section.sectionIcon) : null,
    dishes: section.dishes ? section.dishes.map(transformDishToResponse) : [],
    customSlots: section.customSlots || [],
    specialDays: section.specialDays || [],
    globalCustomSlots: section.globalCustomSlots || [],
    availabilityInfo: section.availabilityInfo
  };
};

/**
 * Transform a Dish entity with availability info into the custom response format
 */
export const transformDishToResponse = (
  dish: Dish & { availabilityInfo: AvailabilityInfo }
): DishResponse => {
  return {
    dishId: dish.dishId,
    pictureUrl: dish.pictureUrl,
    name: dish.name,
    description: dish.description,
    price: dish.price?.toString() || "0",
    dietaryInfo: dish.dietaryInfo || [],
    tags: dish.tags || [],
    isCustomizable: dish.isCustomizable,
    preparationTime: dish.preparationTime,
    availability: transformAvailability(dish.availability),
    visibility: dish.visibility,
    createdAt: dish.createdAt,
    updatedAt: dish.updatedAt,
    deletedAt: dish.deletedAt || null,
    customTimes: dish.customTimes,
    specialRequests: dish.specialRequests,
    customSlots: dish.customSlots || [],
    globalCustomSlots: dish.globalCustomSlots || [],
    specialDays: dish.specialDays || [],
    availabilityInfo: dish.availabilityInfo,
    dishIngredients: dish.dishIngredients ? dish.dishIngredients.map(transformDishIngredientToResponse) : [],
    allergies: dish.allergies ? dish.allergies.map(transformAllergyToResponse) : [],
    customization: dish.customization || undefined
  };
};

/**
 * Transform availability object to ensure consistent format
 */
const transformAvailability = (availability: any): MenuAvailability => {
  return {
    site: availability?.site ?? true,
    dineIn: availability?.dineIn ?? true,
    mobile: availability?.mobile ?? true,
    pickup: availability?.pickup ?? true,
    delivery: availability?.delivery ?? true,
    takeAway: availability?.takeAway ?? true,
    phoneOrder: availability?.phoneOrder ?? true,
    contactlessDineIn: availability?.contactlessDineIn ?? true
  };
};

/**
 * Transform section icon to response format
 */
const transformSectionIconToResponse = (sectionIcon: any): SectionIconResponse => {
  return {
    sectionIconId: sectionIcon.sectionIconId,
    iconName: sectionIcon.iconName,
    iconImg: sectionIcon.iconImg,
    createdAt: sectionIcon.createdAt,
    updatedAt: sectionIcon.updatedAt,
    deletedAt: sectionIcon.deletedAt || null
  };
};

/**
 * Transform dish ingredient to response format
 */
const transformDishIngredientToResponse = (dishIngredient: any): DishIngredientResponse => {
  return {
    dishIngredientId: dishIngredient.dishIngredientId,
    amount: dishIngredient.amount?.toString() || "0",
    createdAt: dishIngredient.createdAt,
    updatedAt: dishIngredient.updatedAt,
    deletedAt: dishIngredient.deletedAt || null,
    ingredient: transformIngredientToResponse(dishIngredient.ingredient)
  };
};

/**
 * Transform ingredient to response format
 */
const transformIngredientToResponse = (ingredient: any): IngredientResponse => {
  return {
    ingredientId: ingredient.ingredientId,
    name: ingredient.name,
    description: ingredient.description,
    unit: ingredient.unit,
    createdAt: ingredient.createdAt,
    updatedAt: ingredient.updatedAt,
    deletedAt: ingredient.deletedAt || null
  };
};

/**
 * Transform allergy to response format
 */
const transformAllergyToResponse = (allergy: any): AllergyResponse => {
  return {
    allergyId: allergy.allergyId,
    name: allergy.name,
    description: allergy.description,
    colorPreference: allergy.colorPreference,
    createdAt: allergy.createdAt,
    updatedAt: allergy.updatedAt,
    deletedAt: allergy.deletedAt || null
  };
};

/**
 * Create the complete API response structure
 */
export const createMenuApiResponse = (
  menu: MenuWithAvailabilityResponse,
  statusCode: number = 200,
  message: string = "Active food menu fetched successfully!"
): MenuApiResponse => {
  return {
    statusCode,
    success: true,
    message,
    data: menu
  };
};
