import { NextFunction, Request, Response } from "express";
import { FoodMenu } from "../../models/foodmenu/foodmenu.model";
import {
  BAD_REQUEST,
  CONFLICT,
  CREATED,
  NOT_FOUND,
  OK,
} from "../../constants/STATUS_CODES";
import { errorHandler } from "../../utils/errorHandler";
import { ResponseHelper } from "../../helpers/system/ResponseHelper.helper";
import { getRepositories } from "../../helpers/system/RepositoryHelper.helper";
import { QueryRunner, Repository } from "typeorm";
import {
  ACTIVE_FOOD_MENU_NOT_FOUND,
  FOOD_MENU_ALREADY_EXISTS,
  FOOD_MENU_NOT_FOUND,
} from "../../constants/tenant/foodmenu/err";
import {
  ACTIVE_FOODMENU_FETCHED,
  ALL_FOOD_MENU_FETCHED,
  FOOD_MENU_CLONED,
  FOOD_MENU_CREATED,
  FOOD_MENU_DELETED,
  FOOD_MENU_FETCHED,
  FOOD_MENU_STATUS_UPDATED,
  FOOD_MENU_UPDATED,
} from "../../constants/tenant/foodmenu/msg";
import { Branch } from "../../models/company/branch.model";
import {
  BRANCH_NOT_FOUND,
  CUSTOM_HOUR_NOT_FOUND,
  DUPLICATE_DAYS_IN_CUSTOMHOURS,
} from "../../constants/tenant/company/err";
import { CustomHourSlot } from "../../models/common/customhourslot.model";
import { SpecialDay } from "../../models/common/specialday.model";
import { Dish } from "../../models/foodmenu/dish.model";
import {
  duplicateAllSectionsFromFoodMenu,
  foodMenuDuplicator,
} from "../../helpers/dish/duplication.helper";
import { applyUpcomingChangesToDishes, applyUpcomingChangesToDishesBatch, extractDishIds, filterUnavailableItems, loadCustomizationDataBatch, mergeMenuData } from "../../helpers/dish/upcomingchange.helper";
import { transformMenuToResponse, createMenuApiResponse } from "../../helpers/dish/menuResponseTransformer.helper";
import { MenuService } from "../../services/menu.service";
import {
  AvailabilityContext,
  ItemWithAvailability,
  MenuAvailabilityService,
  OptimizedMenuAvailabilityService,
} from "../../helpers/dish/dishAvailability.helper";
import { Section } from "../../models/foodmenu/section.model";

export const createFoodMenu = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { FoodMenu, Branch } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
      Branch: Repository<Branch>;
    };

    const branchId = req.params.branchId;

    const exBranch = await Branch.findOne({
      where: {
        branchId,
      },
      relations: ["foodMenus"],
    });

    if (!exBranch) return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));

    const data = req.body as FoodMenu;

    const exFoodMenu = await FoodMenu.findOneBy({
      name: data.name,
    });

    if (exFoodMenu)
      return next(errorHandler(BAD_REQUEST, FOOD_MENU_ALREADY_EXISTS));

    const newFoodMenu = FoodMenu.create(data);

    if (exBranch.foodMenus.length < 1) {
      newFoodMenu.isDefault = true;
    }

    await FoodMenu.save(newFoodMenu);

    exBranch.foodMenus.push(newFoodMenu);

    await Branch.save(exBranch);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, CREATED, FOOD_MENU_CREATED, newFoodMenu);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getAllFoodMenuByBranch = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { Branch } = getRepositories(qRunner) as {
      Branch: Repository<Branch>;
    };

    const branchId = req.params.branchId;

    const exBranch = await Branch.findOne({
      where: {
        branchId,
      },
      relations: ["foodMenus"],
    });

    if (!exBranch) {
      return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));
    }

    ResponseHelper.success(res, OK, ALL_FOOD_MENU_FETCHED, exBranch.foodMenus);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getFoodMenu = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { FoodMenu } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
    };

    const foodMenuId = req.params.foodMenuId;

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        foodMenuId,
      },
      relations: [
        "sections",
        "sections.sectionIcon",
        "sections.dishes",
        "customSlots",
        "specialDays",
      ],
    });

    if (!exFoodMenu) return next(errorHandler(NOT_FOUND, FOOD_MENU_NOT_FOUND));

    ResponseHelper.success(res, OK, FOOD_MENU_FETCHED, exFoodMenu);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchFoodMenuStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { FoodMenu, Branch } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
      Branch: Repository<Branch>;
    };

    const foodMenuId = req.params.foodMenuId;
    const { isDefault } = req.body;

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        foodMenuId,
      },
      relations: ["branch", "tenantSettings.foodMenus"],
    });

    if (!exFoodMenu)
      return next(errorHandler(BAD_REQUEST, FOOD_MENU_NOT_FOUND));

    const parentBranch = exFoodMenu.branch;

    if (isDefault) {
      exFoodMenu.isDefault = isDefault;

      parentBranch.foodMenus.forEach((menu) => {
        if (menu.foodMenuId !== exFoodMenu.foodMenuId) {
          menu.isDefault = false;
        }
      });
    }

    await Branch.save(parentBranch);

    await FoodMenu.save(exFoodMenu);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, FOOD_MENU_STATUS_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchFoodMenuAttr = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { FoodMenu } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
    };

    const foodMenuId = req.params.foodMenuId;
    const updateData = req.body;

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        foodMenuId,
      },
    });

    if (!exFoodMenu)
      return next(errorHandler(BAD_REQUEST, FOOD_MENU_NOT_FOUND));

    if (Object.keys(updateData).length > 0) {
      exFoodMenu.update(updateData);
    }

    await FoodMenu.save(exFoodMenu);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, FOOD_MENU_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const deleteFoodMenu = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();

  try {
    const { FoodMenu } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
    };

    const foodMenuId = req.params.foodMenuId;

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        foodMenuId,
      },
    });

    if (!exFoodMenu) return next(errorHandler(NOT_FOUND, FOOD_MENU_NOT_FOUND));

    await FoodMenu.remove(exFoodMenu);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, FOOD_MENU_DELETED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchFoodMenuCustomHours = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { FoodMenu, CustomHourSlot } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
      CustomHourSlot: Repository<CustomHourSlot>;
    };

    const foodMenuId = req.params.foodMenuId;
    const { customTimes, customSlots, globalCustomSlots } = req.body;

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        foodMenuId,
      },
      relations: [
        "customSlots",
        "globalCustomSlots",
        "branch.businessHour.customSlots",
      ],
    });

    if (!exFoodMenu)
      return next(errorHandler(BAD_REQUEST, FOOD_MENU_NOT_FOUND));

    if (customTimes !== undefined) {
      exFoodMenu.customTimes = customTimes;
      await FoodMenu.save(exFoodMenu);
    }

    if (exFoodMenu.globalCustomSlots && globalCustomSlots) {
      const branchCustomSlotIds =
        exFoodMenu.branch.businessHour.customSlots.map((slot) => slot.slotId);

      for (const slotId of globalCustomSlots) {
        if (!branchCustomSlotIds.includes(slotId)) {
          return next(errorHandler(BAD_REQUEST, CUSTOM_HOUR_NOT_FOUND));
        }
      }

      exFoodMenu.globalCustomSlots = exFoodMenu.globalCustomSlots.filter(
        (slot) => globalCustomSlots.includes(slot.slotId)
      );

      // Add new slots
      for (const slotId of globalCustomSlots) {
        if (
          !exFoodMenu.globalCustomSlots.find((slot) => slot.slotId === slotId)
        ) {
          const customSlot = await CustomHourSlot.findOneBy({ slotId });
          if (customSlot) {
            exFoodMenu.globalCustomSlots.push(customSlot);
          }
        }
      }

      await FoodMenu.save(exFoodMenu);
    }

    if (exFoodMenu.customTimes && customSlots) {
      // Check for duplicate active days across both existing and incoming custom slots
      const customHourDays = new Map<string, boolean>();

      exFoodMenu.customSlots.forEach((slot: any) => {
        if (slot.days && slot.isActive) {
          Object.entries(slot.days).forEach(([day, isActive]) => {
            if (isActive && slot.isActive) {
              customHourDays.set(day, true);
            }
          });
        }
      });

      customSlots.forEach((slot: any) => {
        if (slot.days && slot.isActive) {
          Object.entries(slot.days).forEach(([day, isActive]) => {
            if (isActive && customHourDays.get(day)) {
              return next(
                errorHandler(CONFLICT, DUPLICATE_DAYS_IN_CUSTOMHOURS)
              );
            }
            if (isActive && slot.isActive) {
              customHourDays.set(day, true);
            }
          });
        }
      });

      const existingCustomSlotIds = exFoodMenu.customSlots.map(
        (slot) => slot.slotId
      );
      const incomingCustomSlotIds = customSlots
        .filter((slot: any) => slot.slotId)
        .map((slot: any) => slot.slotId);

      const customSlotsToDelete = exFoodMenu.customSlots.filter(
        (slot) => !incomingCustomSlotIds.includes(slot.slotId)
      );

      if (customSlotsToDelete.length > 0) {
        await CustomHourSlot.remove(customSlotsToDelete);
      }

      for (const slotData of customSlots) {
        if (slotData.slotId) {
          const updateData: Partial<CustomHourSlot> = {};

          if ("name" in slotData) updateData.name = slotData.name;
          if ("days" in slotData) updateData.days = slotData.days;
          if ("is24Hours" in slotData)
            updateData.is24Hours = slotData.is24Hours;
          if ("firstSeating" in slotData)
            updateData.firstSeating = slotData.firstSeating;
          if ("lastSeating" in slotData)
            updateData.lastSeating = slotData.lastSeating;
          if ("isActive" in slotData) updateData.isActive = slotData.isActive;

          await CustomHourSlot.update({ slotId: slotData.slotId }, updateData);
        } else {
          const newCustomSlot = CustomHourSlot.create({
            foodMenu: exFoodMenu,
            name: slotData.name,
            days: slotData.days,
            is24Hours: slotData.is24Hours,
            firstSeating: slotData.firstSeating,
            lastSeating: slotData.lastSeating,
            isActive: slotData.isActive,
          });

          await CustomHourSlot.save(newCustomSlot);
        }
      }
    }

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, FOOD_MENU_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchFoodMenuAvailability = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { FoodMenu } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
    };

    const foodMenuId = req.params.foodMenuId;
    const { availability } = req.body;

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        foodMenuId,
      },
    });

    if (!exFoodMenu)
      return next(errorHandler(BAD_REQUEST, FOOD_MENU_NOT_FOUND));

    if (availability) {
      exFoodMenu.availability = {
        ...exFoodMenu.availability,
        ...availability,
      };
    }

    await FoodMenu.save(exFoodMenu);

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, FOOD_MENU_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const patchFoodMenuSpecialDays = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  await qRunner.startTransaction();
  try {
    const { FoodMenu, SpecialDay } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
      SpecialDay: Repository<SpecialDay>;
    };

    const foodMenuId = req.params.foodMenuId;
    const { specialDays } = req.body;

    const exFoodMenu = await FoodMenu.findOne({
      where: {
        foodMenuId,
      },
      relations: ["specialDays"],
    });

    if (!exFoodMenu)
      return next(errorHandler(BAD_REQUEST, FOOD_MENU_NOT_FOUND));

    if (specialDays && specialDays.length > 0) {
      const existingSpecialDayIds = exFoodMenu.specialDays.map(
        (day) => day.specialDayId
      );
      const incomingSpecialDayIds = specialDays
        .filter((day: any) => day.specialDayId)
        .map((day: any) => day.specialDayId);

      const specialDaysToDelete = exFoodMenu.specialDays.filter(
        (day) => !incomingSpecialDayIds.includes(day.specialDayId)
      );

      if (specialDaysToDelete.length > 0) {
        await SpecialDay.remove(specialDaysToDelete);
      }

      for (const dayData of specialDays) {
        if (dayData.specialDayId) {
          const updateData = dayData;

          const existingSpecialDay = await SpecialDay.findOne({
            where: { specialDayId: dayData.specialDayId },
          });

          if (existingSpecialDay) {
            existingSpecialDay.update(updateData);
            await SpecialDay.save(existingSpecialDay);
          }
        } else {
          const newSpecialDay = SpecialDay.create({
            foodMenu: exFoodMenu,
            eventName: dayData.eventName,
            startTime: dayData.startTime,
            endTime: dayData.endTime,
            availability: dayData.availability,
          });

          await SpecialDay.save(newSpecialDay);
        }
      }
    }

    await qRunner.commitTransaction();

    ResponseHelper.success(res, OK, FOOD_MENU_UPDATED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getActiveMenu = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;

  await qRunner.connect();

  try {
    const { FoodMenu, Branch } = getRepositories(qRunner) as {
      FoodMenu: Repository<FoodMenu>;
      Branch: Repository<Branch>;
    };

    const branchId = req.params.branchId;

    const exBranch = await Branch.findOneBy({
      branchId,
    });

    if (!exBranch) {
      return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));
    }

    const activeFoodMenu = await FoodMenu.findOne({
      where: {
        branch: {
          branchId,
        },
        isDefault: true,
      },
      relations: [
        "sections",
        "sections.sectionIcon",
        "sections.dishes",
        "sections.customSlots",
        "sections.specialDays",
        "sections.globalCustomSlots",
        "sections.dishes.customSlots",
        "sections.dishes.globalCustomSlots",
        "sections.dishes.specialDays",

        "sections.dishes.dishIngredients",
        "sections.dishes.allergies",
        // customizations
        "sections.dishes.customization",
        "sections.dishes.customization.dishSizes",
        "sections.dishes.customization.dishExclusions",
        "sections.dishes.customization.cookingStyles",
        "sections.dishes.customization.spiciness",

        "sections.dishes.customization.dishAddons",
        "sections.dishes.customization.dishExtras",

        "sections.dishes.customization.dishSides",
        "sections.dishes.customization.dishBeverages",
        "sections.dishes.customization.dishDesserts",

        "sections.dishes.customization.dishSides.dish",
        "sections.dishes.customization.dishBeverages.dish",
        "sections.dishes.customization.dishDesserts.dish",

        "customSlots",
        "globalCustomSlots",
        "specialDays",
      ],
      order: {
        createdAt: "ASC",
        sections: {
          createdAt: "ASC",
          dishes: {
            createdAt: "ASC",
          },
        },
      },
    });

    if (!activeFoodMenu)
      return next(errorHandler(NOT_FOUND, ACTIVE_FOOD_MENU_NOT_FOUND));

    const modifiedFoodMenu = { ...activeFoodMenu };

    if (modifiedFoodMenu.sections) {
      for (const section of modifiedFoodMenu.sections) {
        if (section.dishes && section.dishes.length > 0) {
          section.dishes = (await applyUpcomingChangesToDishes(
            section.dishes,
            qRunner,
            activeFoodMenu.foodMenuId
          )) as Dish[];
        }
      }
    }

    ResponseHelper.success(res, OK, ACTIVE_FOODMENU_FETCHED, modifiedFoodMenu);
  } catch (error) {
    next(error);
  } finally {
    await qRunner.release();
  }
};

export const getActiveMenuWithAvailability = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();

  try {
    const { branchId, pos } = req.validatedQuery;
    const forPOS = pos === "true";

    // Use the new MenuService to handle the complex logic
    const menuService = new MenuService(qRunner);
    const transformedMenu = await menuService.getActiveMenuWithAvailability(branchId, forPOS);

    ResponseHelper.success(res, OK, ACTIVE_FOODMENU_FETCHED, transformedMenu);

  } catch (error) {
    if (error.message === "Branch not found") {
      return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));
    }
    if (error.message === "Active food menu not found") {
      return next(errorHandler(NOT_FOUND, ACTIVE_FOOD_MENU_NOT_FOUND));
    }
    next(error);
  } finally {
    await qRunner.release();
  }
};

// export const getActiveMenuWithAvailability = async (
//   req: Request,
//   res: Response,
//   next: NextFunction
// ) => {
//   const qRunner = req.queryRunner;
//   await qRunner.connect();

//   try {
//     const { FoodMenu, Branch } = getRepositories(qRunner) as {
//       FoodMenu: Repository<FoodMenu>;
//       Branch: Repository<Branch>;
//     };

//     const { branchId, pos } = req.validatedQuery;

//     const forPOS = pos === "true";

//     const exBranch = await Branch.findOne({
//       where: { branchId },
//       relations: [
//         "businessHour",
//         "businessHour.slots",
//         "businessHour.customSlots",
//         "businessHour.specialDays",
//       ],
//     });

//     if (!exBranch) {
//       return next(errorHandler(NOT_FOUND, BRANCH_NOT_FOUND));
//     }

//     const activeFoodMenu = await FoodMenu.findOne({
//       where: {
//         branch: { branchId },
//         isDefault: true,
//       },
//       relations: [
//         "sections",
//         "sections.sectionIcon",
//         "sections.dishes",
//         "sections.customSlots",
//         "sections.specialDays",
//         "sections.globalCustomSlots",
//         "sections.dishes.customSlots",
//         "sections.dishes.globalCustomSlots",
//         "sections.dishes.specialDays",
//         "sections.dishes.dishIngredients",
//         "sections.dishes.allergies",
//         "sections.dishes.customization",
//         "sections.dishes.customization.dishSizes",
//         "sections.dishes.customization.dishExclusions",
//         "sections.dishes.customization.cookingStyles",
//         "sections.dishes.customization.spiciness",
//         "sections.dishes.customization.dishAddons",
//         "sections.dishes.customization.dishExtras",
//         "sections.dishes.customization.dishSides",
//         "sections.dishes.customization.dishBeverages",
//         "sections.dishes.customization.dishDesserts",
//         "sections.dishes.customization.dishSides.dish",
//         "sections.dishes.customization.dishBeverages.dish",
//         "sections.dishes.customization.dishDesserts.dish",
//         "customSlots",
//         "globalCustomSlots",
//         "specialDays",
//       ],
//       order: {
//         createdAt: "ASC",
//         sections: {
//           createdAt: "ASC",
//           dishes: {
//             createdAt: "ASC",
//           },
//         },
//       },
//     });

//     if (!activeFoodMenu) {
//       return next(errorHandler(NOT_FOUND, ACTIVE_FOOD_MENU_NOT_FOUND));
//     }

//     // Apply upcoming changes (your existing logic)
//     const modifiedFoodMenu = { ...activeFoodMenu };
//     if (modifiedFoodMenu.sections) {
//       for (const section of modifiedFoodMenu.sections) {
//         if (section.dishes && section.dishes.length > 0) {
//           section.dishes = (await applyUpcomingChangesToDishes(
//             section.dishes,
//             qRunner,
//             activeFoodMenu.foodMenuId
//           )) as Dish[];
//         }
//       }
//     }

//     // Evaluate availability
//     const availabilityService = new MenuAvailabilityService(qRunner);
//     const currentTime = new Date();
//     const dayOfWeek = availabilityService["getDayKey"](currentTime.getDay());

//     const context: AvailabilityContext = {
//       currentTime,
//       dayOfWeek,
//       forPOS,
//     };

//     const menuWithAvailability =
//       await availabilityService.evaluateMenuAvailability(
//         modifiedFoodMenu as FoodMenu,
//         exBranch,
//         context
//       );

//     // For customer app, filter out unavailable items completely
//     if (!forPOS) {
//       type SectionWithAvailability = Section & {
//         availabilityInfo: ItemWithAvailability;
//         dishes: (Dish & { availabilityInfo: ItemWithAvailability })[];
//       };
//       type DishWithAvailability = Dish & {
//         availabilityInfo: ItemWithAvailability;
//       };

//       const sectionsWithAvailability =
//         menuWithAvailability.sections as SectionWithAvailability[];

//       menuWithAvailability.sections = sectionsWithAvailability
//         .filter((section) => section.availabilityInfo.isAvailable)
//         .map((section) => {
//           section.dishes = section.dishes.filter(
//             (dish): dish is DishWithAvailability =>
//               (dish as DishWithAvailability).availabilityInfo.isAvailable
//           );
//           return section;
//         })
//         .filter((section) => section.dishes.length > 0);
//     }

//     ResponseHelper.success(
//       res,
//       OK,
//       ACTIVE_FOODMENU_FETCHED,
//       menuWithAvailability
//     );
//   } catch (error) {
//     next(error);
//   } finally {
//     await qRunner.release();
//   }
// };

export const duplicateFoodMenu = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const qRunner = req.queryRunner;
  await qRunner.connect();
  await qRunner.startTransaction();

  try {
    const foodMenuId = req.params.foodMenuId;
    const clonedFoodMenu = await foodMenuDuplicator(foodMenuId, qRunner);

    await duplicateAllSectionsFromFoodMenu(foodMenuId, clonedFoodMenu, qRunner);

    await qRunner.commitTransaction();
    ResponseHelper.success(res, CREATED, FOOD_MENU_CLONED);
  } catch (error) {
    await qRunner.rollbackTransaction();
    next(error);
  } finally {
    await qRunner.release();
  }
};

