/**
 * Custom response models for Menu API endpoints
 * These interfaces define the structure of the menu data with availability information
 */

export interface AvailabilityInfo {
  isAvailable: boolean;
  reason?: string;
  nextAvailableAt?: Date;
  availableUntil?: Date;
}

export interface MenuAvailability {
  site: boolean;
  dineIn: boolean;
  mobile: boolean;
  pickup: boolean;
  delivery: boolean;
  takeAway: boolean;
  phoneOrder: boolean;
  contactlessDineIn: boolean;
}

export interface SectionIconResponse {
  sectionIconId: string;
  iconName: string;
  iconImg: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export interface IngredientResponse {
  ingredientId: string;
  name: string;
  description: string;
  unit: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export interface DishIngredientResponse {
  dishIngredientId: string;
  amount: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  ingredient: IngredientResponse;
}

export interface AllergyResponse {
  allergyId: string;
  name: string;
  description: string;
  colorPreference: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

export interface DishResponse {
  dishId: string;
  pictureUrl: string;
  name: string;
  description: string;
  price: string;
  dietaryInfo: string[];
  tags: string[];
  isCustomizable: boolean;
  preparationTime: number;
  availability: MenuAvailability;
  visibility: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  customTimes: boolean;
  specialRequests: boolean;
  customSlots: any[];
  globalCustomSlots: any[];
  specialDays: any[];
  availabilityInfo: AvailabilityInfo;
  dishIngredients: DishIngredientResponse[];
  allergies: AllergyResponse[];
  customization?: any;
}

export interface SectionResponse {
  menuSectionId: string;
  pictureUrls: string[];
  name: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  availability: MenuAvailability;
  visibility: boolean;
  customTimes: boolean;
  sectionIcon: SectionIconResponse;
  dishes: DishResponse[];
  customSlots: any[];
  specialDays: any[];
  globalCustomSlots: any[];
  availabilityInfo: AvailabilityInfo;
}

export interface MenuWithAvailabilityResponse {
  foodMenuId: string;
  pictureUrl: string;
  isDefault: boolean;
  name: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  availability: MenuAvailability;
  visibility: boolean;
  customTimes: boolean;
  sections: SectionResponse[];
  customSlots: any[];
  globalCustomSlots: any[];
  specialDays: any[];
  availabilityInfo: AvailabilityInfo;
}

export interface MenuApiResponse {
  statusCode: number;
  success: boolean;
  message: string;
  data: MenuWithAvailabilityResponse;
}
