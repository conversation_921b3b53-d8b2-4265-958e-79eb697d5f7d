import { QueryRunner, Repository } from "typeorm";
import { FoodMenu } from "../models/foodmenu/foodmenu.model";
import { Branch } from "../models/company/branch.model";
import { getRepositories } from "../config/data-source";
import { 
  OptimizedMenuAvailabilityService, 
  AvailabilityContext 
} from "../helpers/dish/dishAvailability.helper";
import { 
  applyUpcomingChangesToDishesBatch, 
  extractDishIds, 
  filterUnavailableItems, 
  loadCustomizationDataBatch, 
  mergeMenuData 
} from "../helpers/dish/upcomingchange.helper";
import { 
  transformMenuToResponse, 
  createMenuApiResponse 
} from "../helpers/dish/menuResponseTransformer.helper";
import { 
  MenuWithAvailabilityResponse, 
  MenuApiResponse 
} from "../types/system/MenuResponse.types";

export class MenuService {
  private queryRunner: QueryRunner;

  constructor(queryRunner: QueryRunner) {
    this.queryRunner = queryRunner;
  }

  /**
   * Get active menu with availability information
   * This is the main method that handles the complex menu data processing
   */
  async getActiveMenuWithAvailability(
    branchId: string, 
    forPOS: boolean = false
  ): Promise<MenuWithAvailabilityResponse> {
    const { FoodMenu, Branch } = getRepositories(this.queryRunner) as {
      FoodMenu: Repository<FoodMenu>;
      Branch: Repository<Branch>;
    };

    // 1. CONCURRENT BRANCH AND MENU LOADING
    const [exBranch, activeFoodMenu] = await Promise.all([
      this.loadBranchWithBusinessHours(Branch, branchId),
      this.loadActiveMenuWithRelations(FoodMenu, branchId)
    ]);

    if (!exBranch) {
      throw new Error("Branch not found");
    }

    if (!activeFoodMenu) {
      throw new Error("Active food menu not found");
    }

    // 2. EVALUATE AVAILABILITY
    const menuWithAvailability = await this.evaluateMenuAvailability(
      activeFoodMenu, 
      exBranch, 
      forPOS
    );

    // 3. FILTER AND PROCESS DISHES
    const availableDishIds = this.getAvailableDishIds(menuWithAvailability, forPOS);
    
    if (availableDishIds.length === 0 && !forPOS) {
      return transformMenuToResponse(filterUnavailableItems(menuWithAvailability));
    }

    // 4. LOAD ADDITIONAL DATA FOR AVAILABLE DISHES
    const [upcomingChanges, customizationData] = await Promise.all([
      applyUpcomingChangesToDishesBatch(availableDishIds, this.queryRunner, activeFoodMenu.foodMenuId),
      loadCustomizationDataBatch(availableDishIds, this.queryRunner)
    ]);

    // 5. MERGE ALL DATA
    const finalMenu = mergeMenuData(
      menuWithAvailability,
      upcomingChanges,
      customizationData,
      forPOS
    );

    // 6. TRANSFORM TO CUSTOM RESPONSE FORMAT
    return transformMenuToResponse(finalMenu);
  }

  /**
   * Load branch with business hours and related data
   */
  private async loadBranchWithBusinessHours(
    branchRepo: Repository<Branch>, 
    branchId: string
  ): Promise<Branch | null> {
    return branchRepo.findOne({
      where: { branchId },
      relations: [
        "businessHour",
        "businessHour.slots", 
        "businessHour.customSlots",
        "businessHour.specialDays",
      ],
    });
  }

  /**
   * Load active menu with all necessary relations
   */
  private async loadActiveMenuWithRelations(
    menuRepo: Repository<FoodMenu>, 
    branchId: string
  ): Promise<FoodMenu | null> {
    return menuRepo.findOne({
      where: { branch: { branchId }, isDefault: true },
      relations: [
        "sections",
        "sections.sectionIcon",
        "sections.dishes",
        "sections.dishes.dishIngredients",
        "sections.dishes.dishIngredients.ingredient",
        "sections.dishes.allergies",
        "sections.customSlots",
        "sections.specialDays", 
        "sections.globalCustomSlots",
        "sections.dishes.customSlots",
        "sections.dishes.globalCustomSlots",
        "sections.dishes.specialDays",
        "customSlots",
        "globalCustomSlots", 
        "specialDays",
      ],
      order: {
        createdAt: "ASC",
        sections: { createdAt: "ASC", dishes: { createdAt: "ASC" } },
      },
    });
  }

  /**
   * Evaluate menu availability using the optimized service
   */
  private async evaluateMenuAvailability(
    menu: FoodMenu, 
    branch: Branch, 
    forPOS: boolean
  ) {
    const availabilityService = new OptimizedMenuAvailabilityService(this.queryRunner);
    const currentTime = new Date();
    const dayOfWeek = availabilityService["getDayKey"](currentTime.getDay());

    const context: AvailabilityContext = {
      currentTime,
      dayOfWeek,
      forPOS,
    };

    return availabilityService.evaluateMenuAvailabilityBatch(menu, branch, context);
  }

  /**
   * Get available dish IDs based on POS flag
   */
  private getAvailableDishIds(menuWithAvailability: any, forPOS: boolean): string[] {
    if (!forPOS) {
      const filteredMenu = filterUnavailableItems(menuWithAvailability);
      return extractDishIds(filteredMenu);
    } else {
      return extractDishIds(menuWithAvailability);
    }
  }

  /**
   * Create a complete API response
   */
  createApiResponse(
    menu: MenuWithAvailabilityResponse,
    statusCode: number = 200,
    message: string = "Active food menu fetched successfully!"
  ): MenuApiResponse {
    return createMenuApiResponse(menu, statusCode, message);
  }
}
